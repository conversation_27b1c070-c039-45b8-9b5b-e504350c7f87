import { useTranslation, type Language } from "@/lib/i18n";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Building, Mail, Phone, Globe, ArrowLeft } from "lucide-react";
import { Link } from "wouter";

interface ImprintProps {
  language: Language;
}

export default function Imprint({ language }: ImprintProps) {
  const { t } = useTranslation(language);

  return (
    <div className="min-h-screen bg-gray-50 py-16">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Back Button */}
        <div className="mb-8">
          <Link href="/">
            <Button variant="ghost" className="gap-2">
              <ArrowLeft className="h-4 w-4" />
              返回首页
            </Button>
          </Link>
        </div>
        {/* Header */}
        <div className="text-center mb-16">
          <div className="gradient-bg rounded-lg w-16 h-16 flex items-center justify-center mx-auto mb-6">
            <Building className="h-8 w-8 text-white" />
          </div>
          <h1 className="text-4xl md:text-5xl font-bold mb-6">
            版权信息
          </h1>
          <p className="text-xl text-gray-600">
            法律信息和公司详情
          </p>
        </div>

        <div className="space-y-8">
          {/* Company Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Building className="h-6 w-6 text-purple-600" />
                <span>公司信息</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h3 className="font-semibold mb-2">Remove bg Inc.</h3>
                <p className="text-gray-600">AI图像处理技术公司</p>
              </div>
              <div>
                <h4 className="font-medium">注册地址：</h4>
                <p className="text-gray-600">
                  123 AI Street<br />
                  San Francisco, CA 94107<br />
                  United States
                </p>
              </div>
              <div>
                <h4 className="font-medium">公司注册号：</h4>
                <p className="text-gray-600">12345678</p>
              </div>
            </CardContent>
          </Card>

          {/* Contact Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Mail className="h-6 w-6 text-purple-600" />
                <span>联系信息</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-3">
                <Mail className="h-5 w-5 text-gray-400" />
                <div>
                  <h4 className="font-medium">电子邮件</h4>
                  <p className="text-gray-600"><EMAIL></p>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <Phone className="h-5 w-5 text-gray-400" />
                <div>
                  <h4 className="font-medium">电话</h4>
                  <p className="text-gray-600">+****************</p>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <Globe className="h-5 w-5 text-gray-400" />
                <div>
                  <h4 className="font-medium">网站</h4>
                  <p className="text-gray-600">www.remove.bg</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Legal Representatives */}
          <Card>
            <CardContent className="p-6">
              <h3 className="font-semibold mb-4">法定代表人</h3>
              <p className="text-gray-600">
                首席执行官：张伟<br />
                首席技术官：李明
              </p>
            </CardContent>
          </Card>

          {/* Regulatory Information */}
          <Card>
            <CardContent className="p-6">
              <h3 className="font-semibold mb-4">监管信息</h3>
              <p className="text-gray-600 mb-2">
                <strong>增值电信业务经营许可证：</strong>B2-20123456
              </p>
              <p className="text-gray-600 mb-2">
                <strong>网络文化经营许可证：</strong>文网文[2021]1234-567号
              </p>
              <p className="text-gray-600">
                <strong>信息安全等级保护备案证明：</strong>1234567890123
              </p>
            </CardContent>
          </Card>

          {/* Disclaimer */}
          <Card>
            <CardContent className="p-6">
              <h3 className="font-semibold mb-4">免责声明</h3>
              <p className="text-gray-600 text-sm leading-relaxed">
                本网站上的信息仅供一般参考。虽然我们努力保持信息的更新和正确，
                但我们不对信息的完整性、准确性、可靠性、适用性或可用性做任何明示或暗示的声明或保证。
                因此，您对此类信息的任何依赖都完全由您自己承担风险。
              </p>
            </CardContent>
          </Card>

          {/* Copyright */}
          <Card>
            <CardContent className="p-6 text-center">
              <p className="text-gray-600">
                © 2025 Remove bg Inc. 保留所有权利。
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}